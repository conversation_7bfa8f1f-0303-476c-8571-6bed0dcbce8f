<template>
  <div class="chat-app">
    <!-- 右侧表格区域 -->
    <div class="right-side">
      <div class="toolbar">
        <el-button type="primary" size="mini" @click="handleSave">保存</el-button>
        <el-button type="success" size="mini" @click="exportToExcel">导出 Excel</el-button>
      </div>
      <div ref="hotTable" class="handsontable-container"></div>
    </div>
  </div>
</template>

<script>
import Handsontable from "handsontable";
import "handsontable/dist/handsontable.full.css";
import ExcelJS from "exceljs";
import HyperFormula from "hyperformula";
import { addExcelData, updateExcelData, getExcelInfo } from "@/api/system/info";
import axios from "axios";
import { getToken } from "@/utils/auth";

const baseURL = process.env.VUE_APP_BASE_API;

export default {
  props: {
    id: {
      type: [String, Number],
      required: true
    }
  },

  data() {
    return {
      hotInstance: null,
      cellStyles: {},
      mergeCells: [],
      hyperformula: null,
      selectedFile: "",
      form: {},
      resizeListener: null, // 窗口大小变化监听器
    };
  },

  async mounted() {
    this.selectedFile = this.id;
    this.form = {
      userexcelId: null,
      userId: null,
      excelId: null,
      exceldocDate: null,
      exceldocContent: null,
      exceldocRemark: null
    };
    try {
      await this.getExcel();
    } catch (error) {
      console.error("加载失败:", error);
      this.$modal.msgError("加载数据失败");
    }
  },

  methods: {
    async getExcel() {
      const response = await getExcelInfo(this.selectedFile);
      this.form = { ...this.form, ...response.data };

      const arrayBuffer = await this.downloadFile(this.form.excelId);
      const savedData = JSON.parse(this.form.exceldocContent);
      await this.loadExcelData(arrayBuffer, savedData);
    },

    async downloadFile(excelId) {
      const url = `${baseURL}/system/excel/download?id=${excelId}`;
      const response = await axios.get(url, {
        responseType: "arraybuffer",
        headers: { Authorization: `Bearer ${getToken()}` }
      });
      return response.data;
    },

    async handleSave() {
      try {
        await this.$modal.confirm("确定要保存数据吗？");
        this.form.exceldocContent = JSON.stringify(this.hotInstance.getData());
        const date = new Date();
        this.form.exceldocDate = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, "0")}-${String(date.getDate()).padStart(2, "0")}`;
        this.form.userId = this.$store.state.user.id;

        if (this.form.userexcelId == null) {
          await addExcelData(this.form);
        } else {
          await updateExcelData(this.form);
        }
        this.$modal.msgSuccess("保存成功");
      } catch (error) {
        if (error !== "cancel") {
          this.$message.error("保存失败: " + (error.message || error));
        }
      }
    },

    async exportToExcel() {
      if (!this.hotInstance) {
        this.$modal.msgError("没有可导出的数据！");
        return;
      }

      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet("Sheet1");

      const data = this.hotInstance.getData();
      data.forEach((row, rowIndex) => {
        const excelRow = worksheet.addRow(row);
        row.forEach((_, colIndex) => {
          const address = this.encodeCellAddress(rowIndex, colIndex);
          const cell = worksheet.getCell(address);
          const style = this.cellStyles[address];

          cell.border = {
            top: { style: "thin", color: { argb: "FF000000" } },
            left: { style: "thin", color: { argb: "FF000000" } },
            bottom: { style: "thin", color: { argb: "FF000000" } },
            right: { style: "thin", color: { argb: "FF000000" } }
          };

          if (style) {
            if (style.backgroundColor) {
              cell.fill = {
                type: "pattern",
                pattern: "solid",
                fgColor: { argb: style.backgroundColor.replace("#", "") }
              };
            }
            if (style.fontColor || style.bold || style.italic || style.fontSize) {
              cell.font = {
                color: { argb: style.fontColor?.replace("#", "") || "FF000000" },
                bold: style.bold || false,
                italic: style.italic || false,
                size: style.fontSize || 12
              };
            }
            if (style.textAlign) {
              cell.alignment = { horizontal: style.textAlign };
            }
          }
        });
      });

      this.mergeCells.forEach(({ row, col, rowspan, colspan }) => {
        const start = this.encodeCellAddress(row, col);
        const end = this.encodeCellAddress(row + rowspan - 1, col + colspan - 1);
        worksheet.mergeCells(`${start}:${end}`);
      });

      const buffer = await workbook.xlsx.writeBuffer();
      const blob = new Blob([buffer], { type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" });
      const link = document.createElement("a");
      link.href = window.URL.createObjectURL(blob);
      link.download = "导出数据.xlsx";
      link.click();

      this.$modal.msgSuccess("Excel 导出成功！");
    },

    async loadExcelData(arrayBuffer, savedData) {
      const workbook = new ExcelJS.Workbook();
      await workbook.xlsx.load(arrayBuffer);
      const worksheet = workbook.getWorksheet(1);

      if (savedData) {
        this.mergeDataToWorksheet(worksheet, savedData);
      }

      const data = this.extractSheetData(worksheet);
      this.cellStyles = this.extractCellStyles(worksheet);
      this.mergeCells = this.extractMergeCells(worksheet);

      this.initHyperFormula();
      this.renderHandsontable(data);

      this.$nextTick(() => {
        this.replaceErrorsWithDefault();
      });
    },

    mergeDataToWorksheet(worksheet, savedData) {
      savedData?.forEach((row, rowIndex) => {
        row.forEach((cellValue, colIndex) => {
          worksheet.getCell(rowIndex + 1, colIndex + 1).value = cellValue;
        });
      });
    },

    replaceErrorsWithDefault() {
      if (!this.hotInstance) return;
      const rowCount = this.hotInstance.countRows();
      const colCount = this.hotInstance.countCols();
      for (let row = 0; row < rowCount; row++) {
        for (let col = 0; col < colCount; col++) {
          const value = this.hotInstance.getDataAtCell(row, col);
          if (value === "#ERROR!") {
            this.hotInstance.setDataAtCell(row, col, "0.00");
          }
        }
      }
    },

    extractSheetData(worksheet) {
      const data = [];
      worksheet.eachRow({ includeEmpty: true }, row => {
        const rowData = [];
        row.eachCell({ includeEmpty: true }, cell => {
          let value = cell.value;
          if (cell.formula) value = `=${cell.formula}`;
          if (value && typeof value === "object") {
            if (value.richText) value = value.richText.map(rt => rt.text).join("");
            if (value.result !== undefined) value = value.result;
          }
          rowData.push(value || "");
        });
        data.push(rowData);
      });
      return data;
    },

    extractMergeCells(worksheet) {
      const mergeCells = [];
      worksheet.model.merges.forEach(merge => {
        const [start, end] = merge.split(":");
        const startCell = this.decodeCellAddress(start);
        const endCell = this.decodeCellAddress(end);
        mergeCells.push({
          row: startCell.row,
          col: startCell.col,
          rowspan: endCell.row - startCell.row + 1,
          colspan: endCell.col - startCell.col + 1
        });
      });
      return mergeCells;
    },

    extractCellStyles(worksheet) {
      const styles = {};
      worksheet.eachRow({ includeEmpty: true }, (row, rowIndex) => {
        row.eachCell({ includeEmpty: true }, (cell, colIndex) => {
          const address = this.encodeCellAddress(rowIndex - 1, colIndex - 1);
          const style = cell.style;
          styles[address] = {
            backgroundColor: style.fill?.fgColor?.argb ? `#${style.fill.fgColor.argb.slice(2)}` : null,
            fontColor: style.font?.color?.argb ? `#${style.font.color.argb.slice(2)}` : null,
            bold: style.font?.bold || false,
            italic: style.font?.italic || false,
            fontSize: style.font?.size || 12,
            textAlign: style.alignment?.horizontal || "left"
          };
        });
      });
      return styles;
    },

    decodeCellAddress(address) {
      const match = address.match(/([A-Z]+)(\d+)/);
      const colLetters = match[1];
      const rowNumber = parseInt(match[2], 10) - 1;
      let colNumber = 0;
      for (let i = 0; i < colLetters.length; i++) {
        colNumber = colNumber * 26 + (colLetters.charCodeAt(i) - 64);
      }
      return { row: rowNumber, col: colNumber - 1 };
    },

    encodeCellAddress(row, col) {
      let colLetters = "";
      col += 1;
      while (col > 0) {
        const remainder = (col - 1) % 26;
        colLetters = String.fromCharCode(65 + remainder) + colLetters;
        col = Math.floor((col - 1) / 26);
      }
      return `${colLetters}${row + 1}`;
    },

    initHyperFormula() {
      this.hyperformula = HyperFormula.buildEmpty({
        licenseKey: "internal-use-in-handsontable"
      });
    },

    renderHandsontable(data) {
      if (this.hotInstance) this.hotInstance.destroy();

      // 计算实际数据的行列范围
      const { maxRow, maxCol } = this.calculateDataRange(data);

      // 设置合适的行列数，确保有足够的空白区域但不会太大
      const displayRows = Math.max(maxRow + 10, 30); // 至少30行，或者数据行数+10行
      const displayCols = Math.max(maxCol + 5, 15);  // 至少15列，或者数据列数+5列

      this.hotInstance = new Handsontable(this.$refs.hotTable, {
        data,
        rowHeaders: true,
        colHeaders: true,
        mergeCells: this.mergeCells,
        formulas: { engine: this.hyperformula },
        licenseKey: "non-commercial-and-evaluation",
        cells: (row, col) => ({
          renderer: this.customRenderer,
          // 启用自动换行
          wordWrap: true
        }),
        // 设置表格尺寸
        width: '100%',
        height: '100%',
        // 自动调整列宽
        colWidths: this.calculateColumnWidths(data, maxCol),
        // 设置显示的行列数
        minRows: displayRows,
        minCols: displayCols,
        maxRows: displayRows + 20, // 允许一些扩展
        maxCols: displayCols + 10,
        // 移除stretchH，让列宽保持计算值，支持水平滚动
        // stretchH: 'all', // 注释掉这行，保持原始列宽
        // 其他配置
        manualColumnResize: true,
        manualRowResize: true,
        contextMenu: true,
        copyPaste: true,
        fillHandle: true,
        // 滚动配置 - 统一滚动行为
        scrollbarWidth: 17,
        scrollbarHeight: 17,
        // 自动行高
        manualRowHeights: true,
        // 启用自动换行
        wordWrap: true,
        // 虚拟滚动配置 - 确保滚动一致性
        renderAllRows: false, // 启用虚拟滚动以提高性能
        renderAllColumns: false, // 启用虚拟列滚动
        viewportRowRenderingOffset: 'auto',
        viewportColumnRenderingOffset: 'auto',
        // 滚动行为配置
        preventOverflow: false, // 允许溢出，确保滚动条正常工作
        // 确保滚动条始终可见
        nativeScrollbars: true
      });

      // 监听窗口大小变化，重新调整表格大小
      this.setupResizeListener();

      // 修复滚动同步问题
      this.fixScrollSync();
    },

    customRenderer(instance, td, row, col, prop, value, cellProperties) {
      Handsontable.renderers.TextRenderer.apply(this, arguments);

      // 设置单元格样式
      const address = this.encodeCellAddress(row, col);
      const style = this.cellStyles[address];
      if (style) {
        td.style.backgroundColor = style.backgroundColor || "";
        td.style.color = style.fontColor || "";
        td.style.fontWeight = style.bold ? "bold" : "";
        td.style.fontStyle = style.italic ? "italic" : "";
        td.style.textAlign = style.textAlign || "";
      }

      // 获取当前列宽
      const colWidth = instance.getColWidth(col);

      // 根据列宽决定文本处理策略
      if (colWidth > 200) {
        // 宽列：允许自动换行
        td.style.whiteSpace = 'pre-wrap';
        td.style.wordWrap = 'break-word';
        td.style.wordBreak = 'break-word'; // 改为break-word，避免强制断词
        td.style.verticalAlign = 'top';
        td.style.lineHeight = '1.4';
        td.style.padding = '4px 6px';

        // 自动调整行高
        if (value && String(value).length > 20) {
          const charWidth = 8;
          const charsPerLine = Math.floor((colWidth - 12) / charWidth);
          const lines = Math.ceil(String(value).length / charsPerLine);
          const minHeight = Math.max(24, lines * 20);

          if (instance.getRowHeight(row) < minHeight) {
            instance.setRowHeight(row, minHeight);
          }
        }
      } else {
        // 窄列：单行显示，超出部分用省略号
        td.style.whiteSpace = 'nowrap';
        td.style.overflow = 'hidden';
        td.style.textOverflow = 'ellipsis';
        td.style.verticalAlign = 'middle';
        td.style.padding = '4px 6px';

        // 添加title属性显示完整内容
        if (value && String(value).length > 15) {
          td.title = String(value);
        }
      }
    },

    // 计算数据的实际范围
    calculateDataRange(data) {
      let maxRow = 0;
      let maxCol = 0;

      for (let row = 0; row < data.length; row++) {
        for (let col = 0; col < data[row].length; col++) {
          const value = data[row][col];
          if (value !== null && value !== undefined && value !== '') {
            maxRow = Math.max(maxRow, row);
            maxCol = Math.max(maxCol, col);
          }
        }
      }

      return { maxRow: maxRow + 1, maxCol: maxCol + 1 };
    },

    // 计算列宽 - 智能适应屏幕宽度
    calculateColumnWidths(data, maxCol) {
      const colWidths = [];
      const minWidth = 80;  // 最小列宽
      const maxWidth = 300; // 增加最大列宽
      const defaultWidth = 120; // 默认列宽

      // 获取容器宽度
      const containerWidth = this.$refs.hotTable ? this.$refs.hotTable.clientWidth : window.innerWidth;
      const availableWidth = containerWidth - 60; // 减去行头和滚动条宽度

      // 第一步：计算每列的理想宽度
      const idealWidths = [];
      let totalIdealWidth = 0;
      let hasContentCols = 0;

      for (let col = 0; col <= maxCol + 5; col++) {
        let maxLength = 0;
        let hasContent = false;

        // 检查该列的所有数据，找出最长的内容
        for (let row = 0; row < data.length; row++) {
          if (data[row] && data[row][col]) {
            const cellValue = String(data[row][col]);
            if (cellValue.trim() !== '') {
              hasContent = true;
              // 计算实际显示长度，中文字符按1.5倍计算
              const displayLength = this.calculateDisplayLength(cellValue);
              maxLength = Math.max(maxLength, displayLength);
            }
          }
        }

        let idealWidth;
        if (!hasContent) {
          idealWidth = defaultWidth;
        } else {
          hasContentCols++;
          // 根据内容长度计算列宽，每个字符大约8px，加上padding
          idealWidth = Math.max(minWidth, Math.min(maxWidth, maxLength * 8 + 30));
        }

        idealWidths.push(idealWidth);
        totalIdealWidth += idealWidth;
      }

      // 第二步：如果总宽度小于可用宽度，按比例扩展有内容的列
      if (totalIdealWidth < availableWidth && hasContentCols > 0) {
        const extraWidth = availableWidth - totalIdealWidth;
        const extraPerCol = extraWidth / hasContentCols;

        for (let col = 0; col < idealWidths.length; col++) {
          if (this.columnHasContent(data, col)) {
            // 有内容的列获得额外宽度，但不超过最大宽度
            const newWidth = Math.min(maxWidth, idealWidths[col] + extraPerCol);
            colWidths.push(newWidth);
          } else {
            colWidths.push(idealWidths[col]);
          }
        }
      } else {
        // 总宽度超过可用宽度，保持原始计算结果，允许水平滚动
        colWidths.push(...idealWidths);
      }

      return colWidths;
    },

    // 检查列是否有内容
    columnHasContent(data, col) {
      for (let row = 0; row < data.length; row++) {
        if (data[row] && data[row][col] && String(data[row][col]).trim() !== '') {
          return true;
        }
      }
      return false;
    },

    // 计算字符串的显示长度（中文字符按1.5倍计算）
    calculateDisplayLength(str) {
      let length = 0;
      for (let i = 0; i < str.length; i++) {
        const char = str.charAt(i);
        // 判断是否为中文字符
        if (/[\u4e00-\u9fa5]/.test(char)) {
          length += 1.5;
        } else {
          length += 1;
        }
      }
      return length;
    },

    // 设置窗口大小变化监听
    setupResizeListener() {
      if (this.resizeListener) {
        window.removeEventListener('resize', this.resizeListener);
      }

      this.resizeListener = () => {
        if (this.hotInstance) {
          this.$nextTick(() => {
            this.hotInstance.render();
          });
        }
      };

      window.addEventListener('resize', this.resizeListener);
    },

    // 修复滚动同步问题
    fixScrollSync() {
      if (!this.hotInstance) return;

      this.$nextTick(() => {
        // 获取主要的滚动容器
        const masterHolder = this.$refs.hotTable.querySelector('.ht_master .wtHolder');
        if (masterHolder) {
          // 确保滚动事件正确传播
          masterHolder.addEventListener('scroll', (e) => {
            // 强制同步所有克隆表格的滚动位置
            const topClone = this.$refs.hotTable.querySelector('.ht_clone_top .wtHolder');
            const leftClone = this.$refs.hotTable.querySelector('.ht_clone_left .wtHolder');

            if (topClone) {
              topClone.scrollLeft = masterHolder.scrollLeft;
            }
            if (leftClone) {
              leftClone.scrollTop = masterHolder.scrollTop;
            }
          });

          // 确保滚动条始终可见
          masterHolder.style.overflowX = 'auto';
          masterHolder.style.overflowY = 'auto';
        }
      });
    }
  },

  // 组件销毁时清理监听器
  beforeDestroy() {
    if (this.resizeListener) {
      window.removeEventListener('resize', this.resizeListener);
    }
    if (this.hotInstance) {
      this.hotInstance.destroy();
    }
  }
};
</script>

<style scoped>
.chat-app {
  display: flex;
  /* 不设置固定高度，让它适应AppMain的布局 */
  width: 100%;
  position: relative;
  overflow: hidden; /* 防止整个页面滚动 */
  /* 计算实际可用高度：100vh - navbar(50px) - tags-view(34px) */
  min-height: calc(100vh - 84px);
}

.right-side {
  flex: 1;
  display: flex;
  flex-direction: column;
  /* 使用min-height而不是固定height，让它适应内容 */
  min-height: calc(100vh - 84px);
  width: 100%;
  overflow: hidden; /* 防止右侧区域滚动 */
}

.toolbar {
  padding: 10px;
  border-bottom: 1px solid #eee;
  background: #f5f5f5;
  flex-shrink: 0; /* 工具栏不收缩 */
  height: 50px; /* 明确设置工具栏高度 */
  z-index: 5; /* 低于固定头部的z-index(9) */
  position: relative;
  /* 确保工具栏不会被固定头部遮挡 */
  box-sizing: border-box;
}

.handsontable-container {
  flex: 1; /* 占用剩余空间 */
  width: 100%;
  /* 计算表格容器高度：总高度 - navbar - tags-view - toolbar */
  height: calc(100vh - 84px - 50px);
  overflow: hidden; /* 让Handsontable内部处理滚动 */
  position: relative;
}

/* 全局样式覆盖，让表格更像Excel */
:deep(.handsontable) {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  font-size: 12px;
}

:deep(.handsontable .ht_master .wtHolder) {
  width: 100% !important;
  height: 100% !important;
}

:deep(.handsontable td) {
  border-right: 1px solid #d0d7de;
  border-bottom: 1px solid #d0d7de;
  background-color: #ffffff;
  /* 移除固定的文本样式，让customRenderer控制 */
}

:deep(.handsontable th) {
  background-color: #f6f8fa;
  border-right: 1px solid #d0d7de;
  border-bottom: 1px solid #d0d7de;
  font-weight: 600;
  color: #24292f;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

:deep(.handsontable .currentRow) {
  background-color: #f6f8fa;
}

:deep(.handsontable .currentCol) {
  background-color: #f6f8fa;
}

:deep(.handsontable .area) {
  background-color: rgba(24, 144, 255, 0.15);
}

/* 修正滚动容器 - 关键修复 */
:deep(.handsontable) {
  height: 100% !important;
  width: 100% !important;
  overflow: hidden !important; /* 表格本身不滚动 */
}

:deep(.handsontable .ht_master) {
  height: 100% !important;
  overflow: hidden !important; /* master容器不滚动 */
}

/* 只有wtHolder才应该有滚动条 - 这是关键 */
:deep(.handsontable .ht_master .wtHolder) {
  overflow: auto !important; /* 只有这里才有滚动 */
  height: 100% !important;
  width: 100% !important;
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f1f1f1;
}

:deep(.handsontable .wtHider) {
  overflow: visible !important;
}

/* 只对主要滚动容器设置滚动条样式 */
:deep(.handsontable .ht_master .wtHolder::-webkit-scrollbar) {
  width: 17px;
  height: 17px;
  display: block !important;
}

:deep(.handsontable .ht_master .wtHolder::-webkit-scrollbar-track) {
  background: #f1f1f1;
  border-radius: 3px;
}

:deep(.handsontable .ht_master .wtHolder::-webkit-scrollbar-thumb) {
  background: #c1c1c1;
  border-radius: 3px;
  min-height: 20px;
  min-width: 20px;
}

:deep(.handsontable .ht_master .wtHolder::-webkit-scrollbar-thumb:hover) {
  background: #a8a8a8;
}

:deep(.handsontable .ht_master .wtHolder::-webkit-scrollbar-corner) {
  background: #f1f1f1;
}

/* 确保表格内容区域正确处理滚动 */
:deep(.handsontable .ht_master .wtHolder) {
  position: relative !important;
  box-sizing: border-box !important;
}

/* 修复虚拟滚动时的显示问题 */
:deep(.handsontable .ht_master table) {
  table-layout: auto !important;
}

/* 确保列头和行头不滚动，但与主内容同步 */
:deep(.handsontable .ht_clone_top .wtHolder),
:deep(.handsontable .ht_clone_left .wtHolder),
:deep(.handsontable .ht_clone_corner .wtHolder) {
  overflow: hidden !important;
}

/* 防止表格内容溢出到工具栏 */
:deep(.handsontable .ht_master .wtHolder) {
  max-height: 100% !important;
}

/* 确保表格不会超出容器边界 */
:deep(.handsontable) {
  position: relative !important;
  z-index: 1 !important;
}

/* 全局样式：确保固定头部始终在最上层 */
:global(.fixed-header) {
  z-index: 1000 !important;
}

:global(.navbar) {
  z-index: 1001 !important;
}

:global(.tags-view) {
  z-index: 1001 !important;
}

/* 确保我们的组件不会覆盖固定头部 */
.chat-app {
  z-index: 1 !important;
  /* 确保组件从正确的位置开始，不会覆盖固定头部 */
  margin-top: 0 !important;
  padding-top: 0 !important;
}

/* 如果页面有固定头部，确保我们的内容从正确位置开始 */
:global(.hasTagsView) .chat-app {
  /* 在有标签页的情况下，确保内容不会被遮挡 */
  position: relative;
  top: 0;
}

/* 确保AppMain的滚动不会影响我们的布局 */
:global(.app-main) {
  /* 重置AppMain的overflow，让我们的组件自己处理滚动 */
  overflow: hidden !important;
}

/* 当我们的页面激活时，确保AppMain有正确的高度 */
:global(.hasTagsView .app-main) {
  height: calc(100vh - 84px) !important;
  min-height: calc(100vh - 84px) !important;
}
</style>
